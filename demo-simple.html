<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B2 First Trainer - Simple Version</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        cambridge: {
                            blue: '#0066CC',
                            green: '#00A651',
                            orange: '#FF6600',
                            purple: '#663399',
                            red: '#CC0000',
                            gray: {
                                50: '#F8F9FA',
                                100: '#E9ECEF',
                                200: '#DEE2E6',
                                300: '#CED4DA',
                                400: '#6C757D',
                                500: '#495057',
                                600: '#343A40',
                                700: '#212529'
                            }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .btn-primary { 
            background-color: #0066CC; 
            color: white; 
            padding: 0.5rem 1rem; 
            border-radius: 0.375rem; 
            font-weight: 500;
            border: none;
            cursor: pointer;
        }
        .btn-primary:hover { background-color: #0052a3; }
        .btn-secondary { 
            background-color: #E9ECEF; 
            color: #495057; 
            padding: 0.5rem 1rem; 
            border-radius: 0.375rem; 
            font-weight: 500;
            border: none;
            cursor: pointer;
        }
        .btn-secondary:hover { background-color: #DEE2E6; }
        .card { 
            background: white; 
            border-radius: 0.5rem; 
            box-shadow: 0 1px 3px rgba(0,0,0,0.1); 
            border: 1px solid #DEE2E6; 
            padding: 1.5rem; 
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold">B2</span>
                    </div>
                    <h1 class="text-xl font-bold text-gray-700">B2 First Trainer</h1>
                    <span class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">AI-Powered</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="showSettings()" class="btn-secondary">Settings</button>
                    <button onclick="showProgress()" class="btn-primary">View Progress</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- Welcome Section -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-700 mb-2">Welcome to B2 First Trainer</h2>
            <p class="text-lg text-gray-600 mb-6">
                Practice for your Cambridge B2 First exam with AI-generated exercises.
                Choose a specific part to practice or take a full test.
            </p>
            
            <!-- Quick Actions -->
            <div class="flex flex-wrap gap-4 mb-8">
                <button onclick="startFullTest()" class="btn-primary flex items-center space-x-2">
                    <span>⏰</span>
                    <span>Take Full Test</span>
                </button>
                <button onclick="startPracticeMode()" class="btn-secondary flex items-center space-x-2">
                    <span>🎯</span>
                    <span>Practice Mode</span>
                </button>
                <button onclick="showProgress()" class="btn-secondary flex items-center space-x-2">
                    <span>📈</span>
                    <span>View Statistics</span>
                </button>
            </div>
        </div>

        <!-- Test Types Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Reading & Use of English -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-blue-600 p-3 rounded-lg">
                        <span class="text-white text-xl">📚</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">Reading & Use of English</h3>
                        <p class="text-gray-600 mb-4">7 parts • 75 minutes • Grammar, vocabulary, and reading comprehension</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Part 1: Multiple Choice Cloze</li>
                                <li>• Part 2: Open Cloze</li>
                                <li>• Part 3: Word Formation</li>
                                <li>• Part 4: Key Word Transformation</li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="startReadingTest()" class="btn-primary text-sm">Start Test</button>
                            <button onclick="selectReadingParts()" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Writing -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-green-600 p-3 rounded-lg">
                        <span class="text-white text-xl">✍️</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">Writing</h3>
                        <p class="text-gray-600 mb-4">2 parts • 80 minutes • Essay and creative writing tasks</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Part 1: Essay (compulsory)</li>
                                <li>• Part 2: Article/Email/Letter/Report/Review</li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="startWritingTest()" class="btn-primary text-sm">Start Test</button>
                            <button onclick="selectWritingParts()" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Listening -->
            <div class="card hover:shadow-md transition-shadow duration-200 opacity-75">
                <div class="flex items-start space-x-4">
                    <div class="bg-orange-600 p-3 rounded-lg">
                        <span class="text-white text-xl">🎧</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">Listening</h3>
                        <p class="text-gray-600 mb-4">4 parts • 40 minutes • Audio comprehension</p>
                        
                        <div class="flex flex-wrap gap-2">
                            <button onclick="showComingSoon('Listening')" class="btn-secondary text-sm">Coming Soon</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Speaking -->
            <div class="card hover:shadow-md transition-shadow duration-200 opacity-75">
                <div class="flex items-start space-x-4">
                    <div class="bg-purple-600 p-3 rounded-lg">
                        <span class="text-white text-xl">🎤</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">Speaking</h3>
                        <p class="text-gray-600 mb-4">4 parts • 14 minutes • Interactive conversation</p>
                        
                        <div class="flex flex-wrap gap-2">
                            <button onclick="showComingSoon('Speaking')" class="btn-secondary text-sm">Coming Soon</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal -->
    <div id="testModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-700" id="modalTitle">Test</h2>
                <button onclick="closeModal()" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
            </div>
            <div id="modalContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Global state
        let currentTest = null;
        let currentPart = 0;
        let currentQuestion = 0;
        let userAnswers = {};
        let practiceMode = false;
        let generatedContent = null;

        // Gemini API configuration
        const GEMINI_API_KEY = 'AIzaSyDbUdjVGol-IsJ4f63JnHNsqWC5huV3iOM';
        const GEMINI_MODEL = 'gemini-2.5-flash-preview-05-20';
        const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent`;

        // Basic functions that WILL work
        function startFullTest() {
            console.log('startFullTest called');
            showTestSelection('full');
        }

        function startPracticeMode() {
            console.log('startPracticeMode called');
            practiceMode = true;
            showTestSelection('practice');
        }

        function showTestSelection(mode) {
            console.log('showTestSelection called with mode:', mode);
            document.getElementById('modalTitle').textContent = mode === 'full' ? 'Full Test Selection' : 'Practice Mode';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-blue-50 border border-blue-500 rounded-lg">
                        <h4 class="text-blue-700 font-medium mb-2">
                            ${mode === 'full' ? '🚀 Full Test Mode' : '🎯 Practice Mode'}
                        </h4>
                        <p class="text-gray-700 text-sm">
                            ${mode === 'full' 
                                ? 'Complete test with realistic time limits and exam conditions.' 
                                : 'Practice specific parts without time pressure.'}
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button onclick="startReadingTest()" class="p-4 border-2 border-blue-500 rounded-lg hover:bg-blue-50 text-left">
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">📚</span>
                                <div>
                                    <h3 class="font-semibold text-gray-700">Reading & Use of English</h3>
                                    <p class="text-sm text-gray-600">7 parts • ${mode === 'full' ? '75 minutes' : 'No time limit'}</p>
                                </div>
                            </div>
                        </button>

                        <button onclick="startWritingTest()" class="p-4 border-2 border-green-500 rounded-lg hover:bg-green-50 text-left">
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">✍️</span>
                                <div>
                                    <h3 class="font-semibold text-gray-700">Writing</h3>
                                    <p class="text-sm text-gray-600">2 parts • ${mode === 'full' ? '80 minutes' : 'No time limit'}</p>
                                </div>
                            </div>
                        </button>
                    </div>

                    <div class="flex justify-center">
                        <button onclick="closeModal()" class="btn-secondary">Back to Dashboard</button>
                    </div>
                </div>
            `;
            openModal();
        }

        function startReadingTest() {
            console.log('startReadingTest called');
            generateAndShowReading();
        }

        function startWritingTest() {
            console.log('startWritingTest called');
            generateAndShowWriting();
        }

        // Gemini API functions
        async function callGeminiAPI(prompt) {
            try {
                console.log('Calling Gemini API...');
                const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }],
                        generationConfig: {
                            temperature: 0.7,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: 2048,
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Gemini API response received');
                return data.candidates[0].content.parts[0].text;
            } catch (error) {
                console.error('Gemini API Error:', error);
                throw error;
            }
        }

        async function generateReadingContent() {
            const topics = [
                'artificial intelligence and society',
                'climate change and environmental protection',
                'social media and digital communication',
                'sustainable tourism and travel',
                'remote work and modern lifestyle',
                'healthy eating and nutrition',
                'urban planning and smart cities',
                'renewable energy and technology',
                'space exploration and technology',
                'online education and learning',
                'sustainable fashion industry',
                'mental health awareness',
                'cryptocurrency and digital economy',
                'virtual reality applications',
                'food waste and sustainability',
                'cultural preservation in digital age'
            ];

            const randomTopic = topics[Math.floor(Math.random() * topics.length)];
            const timestamp = Date.now();
            console.log(`Generating content for topic: ${randomTopic} at ${timestamp}`);

            const prompt = `
You are a Cambridge B2 First exam creator. Create a COMPLETELY NEW Reading & Use of English Part 1 exercise about "${randomTopic}".

IMPORTANT: Generate FRESH, ORIGINAL content. Do NOT repeat previous examples.

Topic: ${randomTopic}
Timestamp: ${timestamp}

Create a text of 180-220 words with 8 gaps. Each gap should test different grammar/vocabulary skills.

REQUIREMENTS:
- Text must be engaging and informative about ${randomTopic}
- 8 gaps testing: articles, prepositions, conjunctions, vocabulary, phrasal verbs, collocations
- Each gap has 4 realistic options where only 1 is clearly correct
- B2 difficulty level (upper-intermediate)

OUTPUT ONLY THIS JSON FORMAT:
{
  "topic": "${randomTopic}",
  "title": "Creative title about ${randomTopic}",
  "text": "Complete text with [GAP1], [GAP2], [GAP3], [GAP4], [GAP5], [GAP6], [GAP7], [GAP8] clearly marked",
  "questions": [
    {
      "gapNumber": 1,
      "options": ["option1", "option2", "option3", "option4"],
      "correctIndex": 0,
      "explanation": "Brief explanation why this is correct"
    },
    {
      "gapNumber": 2,
      "options": ["option1", "option2", "option3", "option4"],
      "correctIndex": 1,
      "explanation": "Brief explanation why this is correct"
    },
    {
      "gapNumber": 3,
      "options": ["option1", "option2", "option3", "option4"],
      "correctIndex": 2,
      "explanation": "Brief explanation why this is correct"
    },
    {
      "gapNumber": 4,
      "options": ["option1", "option2", "option3", "option4"],
      "correctIndex": 3,
      "explanation": "Brief explanation why this is correct"
    },
    {
      "gapNumber": 5,
      "options": ["option1", "option2", "option3", "option4"],
      "correctIndex": 0,
      "explanation": "Brief explanation why this is correct"
    },
    {
      "gapNumber": 6,
      "options": ["option1", "option2", "option3", "option4"],
      "correctIndex": 1,
      "explanation": "Brief explanation why this is correct"
    },
    {
      "gapNumber": 7,
      "options": ["option1", "option2", "option3", "option4"],
      "correctIndex": 2,
      "explanation": "Brief explanation why this is correct"
    },
    {
      "gapNumber": 8,
      "options": ["option1", "option2", "option3", "option4"],
      "correctIndex": 3,
      "explanation": "Brief explanation why this is correct"
    }
  ]
}

Generate completely new content now!`;

            return await callGeminiAPI(prompt);
        }

        async function generateWritingContent() {
            const topics = [
                'technology and happiness',
                'social media and real relationships',
                'environmental responsibility',
                'work-life balance',
                'cultural exchange and travel',
                'education and future careers',
                'healthy lifestyle choices',
                'urban vs rural living',
                'artificial intelligence in daily life',
                'sustainable transportation',
                'digital privacy concerns',
                'remote work culture',
                'fast fashion impact',
                'food security issues',
                'renewable energy adoption',
                'mental health in modern society'
            ];

            const randomTopic = topics[Math.floor(Math.random() * topics.length)];
            const timestamp = Date.now();
            console.log(`Generating writing content for topic: ${randomTopic} at ${timestamp}`);

            const prompt = `
You are a Cambridge B2 First exam creator. Create a COMPLETELY NEW Writing Part 1 essay task about "${randomTopic}".

IMPORTANT: Generate FRESH, ORIGINAL content. Do NOT repeat previous examples.

Topic: ${randomTopic}
Timestamp: ${timestamp}

Create an engaging, thought-provoking essay question that requires students to express opinions and give reasons.

REQUIREMENTS:
- Question must be relevant to B2 students (age 16-25)
- Should encourage critical thinking about ${randomTopic}
- Include 3 bullet points to guide essay structure
- Must be different from previous generations

OUTPUT ONLY THIS JSON FORMAT:
{
  "topic": "${randomTopic}",
  "essayPrompt": "Engaging question or statement about ${randomTopic} that requires discussion and opinion",
  "bulletPoints": [
    "First specific aspect to discuss",
    "Second specific aspect to discuss",
    "........................... (your own idea)"
  ]
}

Generate completely new content now!`;

            return await callGeminiAPI(prompt);
        }

        // Generate and show functions with loading
        async function generateAndShowReading() {
            const startTime = Date.now();
            console.log(`🚀 Starting Reading generation at ${startTime}`);

            showLoadingScreen('🤖 Generating Reading test...', 'Google Gemini AI is creating new questions just for you!');

            try {
                const generatedText = await generateReadingContent();
                console.log('📥 Raw Gemini response received:', generatedText.substring(0, 200) + '...');

                // Try to parse JSON response
                try {
                    // Clean the response - remove any markdown formatting
                    let cleanText = generatedText.trim();
                    if (cleanText.startsWith('```json')) {
                        cleanText = cleanText.replace(/```json\n?/, '').replace(/\n?```$/, '');
                    }
                    if (cleanText.startsWith('```')) {
                        cleanText = cleanText.replace(/```\n?/, '').replace(/\n?```$/, '');
                    }

                    generatedContent = JSON.parse(cleanText);
                    console.log('✅ Successfully parsed content:', generatedContent);
                    console.log(`⏱️ Generation took ${Date.now() - startTime}ms`);
                    showGeneratedReading();
                } catch (parseError) {
                    console.error('❌ Failed to parse JSON:', parseError);
                    console.log('Raw text that failed to parse:', generatedText);
                    showReadingDemo(); // Fallback to static content
                }

            } catch (error) {
                console.error('❌ Failed to generate content:', error);
                showReadingDemo(); // Fallback to static content
            }
        }

        async function generateAndShowWriting() {
            const startTime = Date.now();
            console.log(`🚀 Starting Writing generation at ${startTime}`);

            showLoadingScreen('✍️ Generating Writing topic...', 'Google Gemini AI is creating a personalized essay topic!');

            try {
                const generatedText = await generateWritingContent();
                console.log('📥 Raw Gemini response received:', generatedText.substring(0, 200) + '...');

                try {
                    // Clean the response - remove any markdown formatting
                    let cleanText = generatedText.trim();
                    if (cleanText.startsWith('```json')) {
                        cleanText = cleanText.replace(/```json\n?/, '').replace(/\n?```$/, '');
                    }
                    if (cleanText.startsWith('```')) {
                        cleanText = cleanText.replace(/```\n?/, '').replace(/\n?```$/, '');
                    }

                    generatedContent = JSON.parse(cleanText);
                    console.log('✅ Successfully parsed writing content:', generatedContent);
                    console.log(`⏱️ Generation took ${Date.now() - startTime}ms`);
                    showGeneratedWriting();
                } catch (parseError) {
                    console.error('❌ Failed to parse JSON:', parseError);
                    console.log('Raw text that failed to parse:', generatedText);
                    showWritingDemo(); // Fallback to static content
                }

            } catch (error) {
                console.error('❌ Failed to generate content:', error);
                showWritingDemo(); // Fallback to static content
            }
        }

        function showLoadingScreen(title, description) {
            document.getElementById('modalTitle').textContent = 'AI Content Generation';
            document.getElementById('modalContent').innerHTML = `
                <div class="text-center py-12">
                    <div class="mb-6">
                        <div class="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto"></div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-4">${title}</h3>
                    <p class="text-gray-600 mb-6">${description}</p>
                    <div class="max-w-md mx-auto">
                        <div class="bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 60%"></div>
                        </div>
                        <p class="text-sm text-gray-500 mt-2">This may take a few seconds...</p>
                    </div>
                </div>
            `;
            openModal();
        }

        function showGeneratedReading() {
            if (!generatedContent) {
                showReadingDemo();
                return;
            }

            document.getElementById('modalTitle').textContent = 'Reading & Use of English - Part 1 (AI Generated)';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <!-- AI Generated Badge -->
                    <div class="p-3 bg-green-50 border border-green-500 rounded-lg">
                        <div class="flex items-center space-x-2">
                            <span class="text-green-600">🤖</span>
                            <span class="text-green-700 font-medium">AI Generated Content</span>
                            <span class="text-sm text-green-600">Topic: ${generatedContent.topic}</span>
                        </div>
                    </div>

                    <div class="p-4 bg-blue-50 border border-blue-500 rounded-lg">
                        <h4 class="text-blue-700 font-medium mb-2">Part 1: Multiple Choice Cloze</h4>
                        <p class="text-gray-700 text-sm">For questions 1-8, read the text below and decide which answer (A, B, C or D) best fits each gap.</p>
                    </div>

                    <div class="prose max-w-none">
                        <h3 class="text-lg font-semibold mb-4">${generatedContent.title || 'Reading Text'}</h3>
                        <div class="text-gray-700 leading-relaxed">
                            ${generatedContent.text.replace(/\[GAP(\d+)\]/g, (match, num) => {
                                return `<span class="bg-blue-600 text-white px-2 py-1 rounded font-medium">${num}</span>`;
                            })}
                        </div>
                    </div>

                    <div class="space-y-4">
                        ${generatedContent.questions.map((q, index) => `
                            <div class="border-2 border-blue-500 rounded-lg p-4 bg-blue-50">
                                <h4 class="font-medium text-gray-700 mb-3">Question ${q.gapNumber}:</h4>
                                <div class="space-y-2">
                                    ${q.options.map((option, optionIndex) => `
                                        <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                            <input type="radio"
                                                   name="q${q.gapNumber}"
                                                   value="${String.fromCharCode(65 + optionIndex)}"
                                                   onchange="saveAnswer('q${q.gapNumber}', '${String.fromCharCode(65 + optionIndex)}', ${q.correctIndex}, ${optionIndex})"
                                                   class="text-blue-600">
                                            <span><strong>${String.fromCharCode(65 + optionIndex)}</strong> ${option}</span>
                                        </label>
                                    `).join('')}
                                </div>
                                <div id="explanation${q.gapNumber}" class="mt-3 p-3 bg-green-50 border border-green-500 rounded hidden">
                                    <p class="text-sm text-green-700"><strong>Explanation:</strong> ${q.explanation}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>

                    <div class="flex justify-between items-center pt-4 border-t">
                        <span class="text-sm text-gray-600">AI Generated • Questions 1-8 of 8 • Part 1</span>
                        <div class="space-x-2">
                            <button onclick="showAllExplanations()" class="btn-secondary">Show Explanations</button>
                            <button onclick="showResults()" class="btn-primary">Finish & Review</button>
                            <button onclick="generateAndShowReading()" class="btn-primary">🔄 Generate New</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function showGeneratedWriting() {
            if (!generatedContent) {
                showWritingDemo();
                return;
            }

            document.getElementById('modalTitle').textContent = 'Writing - Essay (AI Generated)';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <!-- AI Generated Badge -->
                    <div class="p-3 bg-green-50 border border-green-500 rounded-lg">
                        <div class="flex items-center space-x-2">
                            <span class="text-green-600">🤖</span>
                            <span class="text-green-700 font-medium">AI Generated Content</span>
                            <span class="text-sm text-green-600">Topic: ${generatedContent.topic}</span>
                        </div>
                    </div>

                    <div class="p-4 bg-green-50 border border-green-500 rounded-lg">
                        <h4 class="text-green-700 font-medium mb-2">Part 1: Essay (Compulsory)</h4>
                        <p class="text-gray-700 text-sm">Write an essay of <strong>140-190 words</strong> using all the notes provided.</p>
                    </div>

                    <div class="p-6 bg-white border-2 border-gray-200 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-700 mb-4">Essay Topic:</h3>
                        <p class="text-gray-700 leading-relaxed mb-4">
                            <strong>${generatedContent.essayPrompt}</strong>
                        </p>
                        <div class="bg-gray-50 p-4 rounded">
                            <h4 class="font-medium mb-2">Write about:</h4>
                            <ul class="space-y-1 text-sm">
                                ${generatedContent.bulletPoints.map((point, index) => `
                                    <li>${index + 1}. ${point}</li>
                                `).join('')}
                            </ul>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-700">Your Essay:</h4>
                            <div class="text-sm font-medium" id="wordCount">0 / 140-190 words</div>
                        </div>

                        <textarea
                            id="essayText"
                            class="w-full h-64 p-4 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                            placeholder="Start writing your essay here..."
                            oninput="updateWordCount()"
                        ></textarea>

                        <div class="grid grid-cols-4 gap-4 text-sm text-center">
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-blue-600" id="wordCountDisplay">0</div>
                                <div class="text-gray-600">Words</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-green-600" id="paragraphCount">0</div>
                                <div class="text-gray-600">Paragraphs</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-purple-600" id="sentenceCount">0</div>
                                <div class="text-gray-600">Sentences</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-orange-600" id="progressPercent">0%</div>
                                <div class="text-gray-600">Complete</div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center pt-4 border-t">
                        <span class="text-sm text-gray-600">AI Generated • Part 1 • Essay</span>
                        <div class="space-x-2">
                            <button onclick="submitEssay()" class="btn-primary">Submit Essay</button>
                            <button onclick="generateAndShowWriting()" class="btn-primary">🔄 Generate New Topic</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function showSettings() {
            alert('⚙️ Settings\n\nSettings panel would open here with options for:\n• Timer preferences\n• Difficulty levels\n• Notification settings');
        }

        function showProgress() {
            alert('📊 Progress & Statistics\n\nProgress panel would show:\n• Performance analytics\n• Strengths and weaknesses\n• Study recommendations');
        }

        function showReadingDemo() {
            console.log('showReadingDemo called');
            document.getElementById('modalTitle').textContent = 'Reading & Use of English - Part 1';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-blue-50 border border-blue-500 rounded-lg">
                        <h4 class="text-blue-700 font-medium mb-2">Part 1: Multiple Choice Cloze</h4>
                        <p class="text-gray-700 text-sm">For questions 1-8, read the text below and decide which answer (A, B, C or D) best fits each gap.</p>
                    </div>

                    <div class="prose max-w-none">
                        <h3 class="text-lg font-semibold mb-4">The Digital Revolution</h3>
                        <p class="text-gray-700 leading-relaxed">
                            The internet has fundamentally changed how we communicate and access information.
                            Social media platforms have <span class="bg-blue-600 text-white px-2 py-1 rounded font-medium">1</span>
                            people to connect with others around the world instantly. However, this technological advancement
                            has also <span class="bg-blue-600 text-white px-2 py-1 rounded font-medium">2</span>
                            new challenges for society.
                        </p>
                    </div>

                    <div class="space-y-4">
                        <div class="border-2 border-blue-500 rounded-lg p-4 bg-blue-50">
                            <h4 class="font-medium text-gray-700 mb-3">Question 1:</h4>
                            <div class="space-y-2">
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="A" onchange="saveAnswer('q1', 'A')" class="text-blue-600">
                                    <span><strong>A</strong> enabled</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="B" onchange="saveAnswer('q1', 'B')" class="text-blue-600">
                                    <span><strong>B</strong> allowed</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="C" onchange="saveAnswer('q1', 'C')" class="text-blue-600">
                                    <span><strong>C</strong> permitted</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="D" onchange="saveAnswer('q1', 'D')" class="text-blue-600">
                                    <span><strong>D</strong> let</span>
                                </label>
                            </div>
                        </div>

                        <div class="border-2 border-purple-500 rounded-lg p-4">
                            <h4 class="font-medium text-gray-700 mb-3">Question 2:</h4>
                            <div class="space-y-2">
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="A" onchange="saveAnswer('q2', 'A')" class="text-purple-600">
                                    <span><strong>A</strong> created</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="B" onchange="saveAnswer('q2', 'B')" class="text-purple-600">
                                    <span><strong>B</strong> made</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="C" onchange="saveAnswer('q2', 'C')" class="text-purple-600">
                                    <span><strong>C</strong> formed</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="D" onchange="saveAnswer('q2', 'D')" class="text-purple-600">
                                    <span><strong>D</strong> built</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center pt-4 border-t">
                        <span class="text-sm text-gray-600">Question 1-2 of 8 • Part 1 of 7</span>
                        <div class="space-x-2">
                            <button onclick="flagQuestion()" class="btn-secondary">🏳️ Flag for Review</button>
                            <button onclick="nextQuestion()" class="btn-primary">Next Question</button>
                            <button onclick="showResults()" class="btn-primary">Finish & Review</button>
                        </div>
                    </div>
                </div>
            `;
            openModal();
        }

        function showWritingDemo() {
            console.log('showWritingDemo called');
            document.getElementById('modalTitle').textContent = 'Writing - Essay';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-green-50 border border-green-500 rounded-lg">
                        <h4 class="text-green-700 font-medium mb-2">Part 1: Essay (Compulsory)</h4>
                        <p class="text-gray-700 text-sm">Write an essay of <strong>140-190 words</strong> using all the notes provided.</p>
                    </div>

                    <div class="p-6 bg-white border-2 border-gray-200 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-700 mb-4">Essay Topic:</h3>
                        <p class="text-gray-700 leading-relaxed mb-4">
                            <strong>Technology has made our lives easier, but has it made us happier? Discuss.</strong>
                        </p>
                        <div class="bg-gray-50 p-4 rounded">
                            <h4 class="font-medium mb-2">Write about:</h4>
                            <ul class="space-y-1 text-sm">
                                <li>1. Communication and relationships</li>
                                <li>2. Work and productivity</li>
                                <li>3. ........................... (your own idea)</li>
                            </ul>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-700">Your Essay:</h4>
                            <div class="text-sm font-medium" id="wordCount">0 / 140-190 words</div>
                        </div>

                        <textarea
                            id="essayText"
                            class="w-full h-64 p-4 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                            placeholder="Start writing your essay here..."
                            oninput="updateWordCount()"
                        ></textarea>

                        <div class="grid grid-cols-4 gap-4 text-sm text-center">
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-blue-600" id="wordCountDisplay">0</div>
                                <div class="text-gray-600">Words</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-green-600" id="paragraphCount">0</div>
                                <div class="text-gray-600">Paragraphs</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-purple-600" id="sentenceCount">0</div>
                                <div class="text-gray-600">Sentences</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-orange-600" id="progressPercent">0%</div>
                                <div class="text-gray-600">Complete</div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center pt-4 border-t">
                        <span class="text-sm text-gray-600">Part 1 • Essay (Compulsory)</span>
                        <div class="space-x-2">
                            <button onclick="saveDraft()" class="btn-secondary">Save Draft</button>
                            <button onclick="submitEssay()" class="btn-primary">Submit Essay</button>
                        </div>
                    </div>
                </div>
            `;
            openModal();
        }

        function saveAnswer(questionId, answer, correctIndex, selectedIndex) {
            console.log('Answer saved:', questionId, answer);
            userAnswers[questionId] = {
                answer: answer,
                isCorrect: correctIndex === selectedIndex,
                correctIndex: correctIndex,
                selectedIndex: selectedIndex
            };

            // Show explanation if answer is selected
            if (correctIndex !== undefined) {
                const explanationEl = document.getElementById(`explanation${questionId.replace('q', '')}`);
                if (explanationEl) {
                    explanationEl.classList.remove('hidden');
                }
            }
        }

        function showAllExplanations() {
            // Show all explanation divs
            document.querySelectorAll('[id^="explanation"]').forEach(el => {
                el.classList.remove('hidden');
            });
        }

        function updateWordCount() {
            const textarea = document.getElementById('essayText');
            if (!textarea) return;

            const text = textarea.value;
            const words = text.trim().split(/\s+/).filter(word => word.length > 0);
            const paragraphs = text.split('\n\n').filter(p => p.trim()).length;
            const sentences = text.split(/[.!?]+/).filter(s => s.trim()).length;

            const wordCount = words.length;
            const progress = Math.round((wordCount / 190) * 100);

            document.getElementById('wordCountDisplay').textContent = wordCount;
            document.getElementById('paragraphCount').textContent = paragraphs;
            document.getElementById('sentenceCount').textContent = sentences;
            document.getElementById('progressPercent').textContent = progress + '%';

            const wordCountEl = document.getElementById('wordCount');
            if (wordCount < 140) {
                wordCountEl.textContent = `${wordCount} / 140-190 words`;
                wordCountEl.className = 'text-sm font-medium text-orange-600';
            } else if (wordCount > 190) {
                wordCountEl.textContent = `${wordCount} / 140-190 words`;
                wordCountEl.className = 'text-sm font-medium text-red-600';
            } else {
                wordCountEl.textContent = `${wordCount} / 140-190 words`;
                wordCountEl.className = 'text-sm font-medium text-green-600';
            }
        }

        function flagQuestion() {
            alert('🏳️ Question flagged for review!');
        }

        function nextQuestion() {
            alert('➡️ Moving to next question...');
        }

        function showResults() {
            console.log('showResults called');
            document.getElementById('modalTitle').textContent = 'Test Results';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="text-center p-6 bg-green-50 rounded-lg">
                        <h3 class="text-2xl font-bold text-green-600 mb-2">🎉 Test Completed!</h3>
                        <p class="text-gray-600">Great job! Here's your performance summary.</p>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">${Object.keys(userAnswers).length}</div>
                            <div class="text-sm text-gray-600">Questions Attempted</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">85%</div>
                            <div class="text-sm text-gray-600">Estimated Score</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600">B2</div>
                            <div class="text-sm text-gray-600">Level</div>
                        </div>
                    </div>

                    <div class="p-4 bg-blue-50 border border-blue-500 rounded-lg">
                        <h4 class="text-blue-700 font-medium mb-2">📊 Detailed Review:</h4>
                        <div class="space-y-2 text-sm">
                            ${Object.entries(userAnswers).map(([q, a]) => `
                                <div class="flex justify-between">
                                    <span>${q}:</span>
                                    <span class="font-medium">${a}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="flex justify-center space-x-4">
                        <button onclick="startNewTest()" class="btn-primary">Start New Test</button>
                        <button onclick="closeModal()" class="btn-secondary">Back to Dashboard</button>
                    </div>
                </div>
            `;
        }

        function saveDraft() {
            alert('✅ Draft saved successfully!');
        }

        function submitEssay() {
            alert('📝 Essay submitted for review!');
            showResults();
        }

        function startNewTest() {
            userAnswers = {};
            closeModal();
            startFullTest();
        }

        function showComingSoon(module) {
            alert(`🚧 ${module} Module Coming Soon!\n\nThis module is in development and will include:\n• Authentic exercises\n• AI-generated content\n• Realistic exam conditions`);
        }

        function openModal() {
            document.getElementById('testModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('testModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('testModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        console.log('Script loaded successfully');
    </script>
</body>
</html>
