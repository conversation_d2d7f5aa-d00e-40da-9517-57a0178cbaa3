<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B2 First Trainer - Cambridge Exam Preparation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        cambridge: {
                            blue: '#0066CC',
                            green: '#00A651',
                            orange: '#FF6600',
                            purple: '#663399',
                            red: '#CC0000',
                            gray: {
                                50: '#F8F9FA',
                                100: '#E9ECEF',
                                200: '#DEE2E6',
                                300: '#CED4DA',
                                400: '#6C757D',
                                500: '#495057',
                                600: '#343A40',
                                700: '#212529'
                            }
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .btn-primary { @apply bg-cambridge-blue text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 font-medium; }
        .btn-secondary { @apply bg-cambridge-gray-100 text-cambridge-gray-700 px-4 py-2 rounded-md hover:bg-cambridge-gray-200 transition-colors duration-200 font-medium; }
        .card { @apply bg-white rounded-lg shadow-sm border border-cambridge-gray-200 p-6; }
    </style>
</head>
<body class="bg-cambridge-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-cambridge-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-cambridge-blue rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">🎯</span>
                        </div>
                        <h1 class="text-xl font-bold text-cambridge-gray-700">B2 First Trainer</h1>
                    </div>
                    <span class="text-sm text-cambridge-gray-500 bg-cambridge-gray-100 px-2 py-1 rounded">AI-Powered</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="btn-secondary">Settings</button>
                    <button class="btn-primary">View Progress</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Welcome Section -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-cambridge-gray-700 mb-2">Welcome to B2 First Trainer</h2>
            <p class="text-lg text-cambridge-gray-600 mb-6">
                Practice for your Cambridge B2 First exam with AI-generated exercises that adapt to your level.
                Choose a specific part to practice or take a full test.
            </p>
            
            <!-- Quick Actions -->
            <div class="flex flex-wrap gap-4 mb-8">
                <button onclick="startFullTest()" class="btn-primary flex items-center space-x-2">
                    <span>⏰</span>
                    <span>Take Full Test</span>
                </button>
                <button onclick="startPracticeMode()" class="btn-secondary flex items-center space-x-2">
                    <span>🎯</span>
                    <span>Practice Mode</span>
                </button>
                <button class="btn-secondary flex items-center space-x-2">
                    <span>📈</span>
                    <span>View Statistics</span>
                </button>
            </div>
        </div>

        <!-- Test Types Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" id="testTypesGrid">
            <!-- Reading & Use of English -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-cambridge-blue p-3 rounded-lg">
                        <span class="text-white text-xl">📚</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-cambridge-gray-700 mb-2">Reading & Use of English</h3>
                        <p class="text-cambridge-gray-600 mb-4">7 parts • 75 minutes • Grammar, vocabulary, and reading comprehension</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-cambridge-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-cambridge-gray-600 space-y-1">
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 1: Multiple Choice Cloze</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 2: Open Cloze</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 3: Word Formation</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 4: Key Word Transformation</span>
                                </li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="startTest('reading-use-of-english', 'full')" class="btn-primary text-sm">Full Test</button>
                            <button onclick="startTest('reading-use-of-english', 'parts')" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Writing -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-cambridge-green p-3 rounded-lg">
                        <span class="text-white text-xl">✍️</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-cambridge-gray-700 mb-2">Writing</h3>
                        <p class="text-cambridge-gray-600 mb-4">2 parts • 80 minutes • Essay and creative writing tasks</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-cambridge-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-cambridge-gray-600 space-y-1">
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 1: Essay (compulsory)</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 2: Article/Email/Letter/Report/Review</span>
                                </li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="startTest('writing', 'full')" class="btn-primary text-sm">Full Test</button>
                            <button onclick="startTest('writing', 'parts')" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Listening -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-cambridge-orange p-3 rounded-lg">
                        <span class="text-white text-xl">🎧</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-cambridge-gray-700 mb-2">Listening</h3>
                        <p class="text-cambridge-gray-600 mb-4">4 parts • 40 minutes • Audio comprehension and note-taking</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-cambridge-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-cambridge-gray-600 space-y-1">
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 1: Multiple Choice</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 2: Sentence Completion</span>
                                </li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="showComingSoon('Listening')" class="btn-primary text-sm">Full Test</button>
                            <button onclick="showComingSoon('Listening')" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Speaking -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-cambridge-purple p-3 rounded-lg">
                        <span class="text-white text-xl">🎤</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-cambridge-gray-700 mb-2">Speaking</h3>
                        <p class="text-cambridge-gray-600 mb-4">4 parts • 14 minutes • Interactive conversation and presentation</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-cambridge-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-cambridge-gray-600 space-y-1">
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 1: Interview</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 2: Long Turn</span>
                                </li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="showComingSoon('Speaking')" class="btn-primary text-sm">Full Test</button>
                            <button onclick="showComingSoon('Speaking')" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center p-6">
                <div class="bg-cambridge-blue p-3 rounded-full w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                    <span class="text-white text-xl">🤖</span>
                </div>
                <h3 class="text-lg font-semibold text-cambridge-gray-700 mb-2">AI-Generated Content</h3>
                <p class="text-cambridge-gray-600">Dynamic exercises created by Google Gemini AI, ensuring fresh content every time you practice.</p>
            </div>
            
            <div class="text-center p-6">
                <div class="bg-cambridge-green p-3 rounded-full w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                    <span class="text-white text-xl">⏰</span>
                </div>
                <h3 class="text-lg font-semibold text-cambridge-gray-700 mb-2">Realistic Timing</h3>
                <p class="text-cambridge-gray-600">Practice with exact time limits from the real exam, or use practice mode without time pressure.</p>
            </div>
            
            <div class="text-center p-6">
                <div class="bg-cambridge-orange p-3 rounded-full w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                    <span class="text-white text-xl">📈</span>
                </div>
                <h3 class="text-lg font-semibold text-cambridge-gray-700 mb-2">Detailed Feedback</h3>
                <p class="text-cambridge-gray-600">Get instant AI-powered feedback with explanations and tips to improve your performance.</p>
            </div>
        </div>
    </main>

    <!-- Demo Test Modal -->
    <div id="testModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-cambridge-gray-700" id="modalTitle">Demo Test</h2>
                <button onclick="closeModal()" class="text-cambridge-gray-500 hover:text-cambridge-gray-700 text-2xl">&times;</button>
            </div>
            <div id="modalContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Global state
        let currentView = 'dashboard';
        let currentTest = null;
        let currentPart = 0;
        let currentQuestion = 0;
        let userAnswers = {};
        let practiceMode = false;
        let timeRemaining = 0;
        let timerInterval = null;
        let generatedContent = null;
        let isGenerating = false;

        // Gemini API configuration
        const GEMINI_API_KEY = 'AIzaSyDbUdjVGol-IsJ4f63JnHNsqWC5huV3iOM';
        const GEMINI_MODEL = 'gemini-2.5-flash-preview-05-20';
        const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent`;

        function startFullTest() {
            showTestSelection('full-test');
        }

        function startPracticeMode() {
            practiceMode = true;
            showTestSelection('practice');
        }

        function showTestSelection(mode) {
            document.getElementById('modalTitle').textContent = mode === 'full-test' ? 'Full Test Selection' : 'Practice Mode Selection';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-blue-50 border border-cambridge-blue rounded-lg">
                        <h4 class="text-sm font-medium text-cambridge-blue mb-2">
                            ${mode === 'full-test' ? '🚀 Full Test Mode' : '🎯 Practice Mode'}
                        </h4>
                        <p class="text-sm text-cambridge-gray-700">
                            ${mode === 'full-test'
                                ? 'Complete all parts with realistic time limits and exam conditions.'
                                : 'Choose specific parts to practice without time pressure.'}
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button onclick="startTest('reading-use-of-english', '${mode}')"
                                class="p-4 border-2 border-cambridge-blue rounded-lg hover:bg-blue-50 text-left">
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">📚</span>
                                <div>
                                    <h3 class="font-semibold text-cambridge-gray-700">Reading & Use of English</h3>
                                    <p class="text-sm text-cambridge-gray-600">7 parts • ${mode === 'full-test' ? '75 minutes' : 'No time limit'}</p>
                                </div>
                            </div>
                        </button>

                        <button onclick="startTest('writing', '${mode}')"
                                class="p-4 border-2 border-cambridge-green rounded-lg hover:bg-green-50 text-left">
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">✍️</span>
                                <div>
                                    <h3 class="font-semibold text-cambridge-gray-700">Writing</h3>
                                    <p class="text-sm text-cambridge-gray-600">2 parts • ${mode === 'full-test' ? '80 minutes' : 'No time limit'}</p>
                                </div>
                            </div>
                        </button>

                        <button onclick="showComingSoon('Listening')"
                                class="p-4 border-2 border-cambridge-orange rounded-lg hover:bg-orange-50 text-left opacity-75">
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">🎧</span>
                                <div>
                                    <h3 class="font-semibold text-cambridge-gray-700">Listening</h3>
                                    <p class="text-sm text-cambridge-gray-600">4 parts • 40 minutes</p>
                                    <span class="text-xs bg-cambridge-orange text-white px-2 py-1 rounded">Coming Soon</span>
                                </div>
                            </div>
                        </button>

                        <button onclick="showComingSoon('Speaking')"
                                class="p-4 border-2 border-cambridge-purple rounded-lg hover:bg-purple-50 text-left opacity-75">
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">🎤</span>
                                <div>
                                    <h3 class="font-semibold text-cambridge-gray-700">Speaking</h3>
                                    <p class="text-sm text-cambridge-gray-600">4 parts • 14 minutes</p>
                                    <span class="text-xs bg-cambridge-purple text-white px-2 py-1 rounded">Coming Soon</span>
                                </div>
                            </div>
                        </button>
                    </div>

                    <div class="flex justify-center pt-4">
                        <button onclick="closeModal()" class="btn-secondary">Back to Dashboard</button>
                    </div>
                </div>
            `;
            document.getElementById('testModal').classList.remove('hidden');
        }

        function startTest(testType, mode) {
            practiceMode = mode === 'practice';

            if (testType === 'reading-use-of-english') {
                generateAndInitializeReadingTest();
            } else if (testType === 'writing') {
                generateAndInitializeWritingTest();
            } else {
                showComingSoon(testType);
            }
        }

        // Gemini API functions
        async function callGeminiAPI(prompt) {
            try {
                const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }],
                        generationConfig: {
                            temperature: 0.7,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: 2048,
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                return data.candidates[0].content.parts[0].text;
            } catch (error) {
                console.error('Gemini API Error:', error);
                throw error;
            }
        }

        async function generateReadingContent() {
            const topics = [
                'artificial intelligence and society',
                'climate change and environmental protection',
                'social media and digital communication',
                'sustainable tourism and travel',
                'remote work and modern lifestyle',
                'healthy eating and nutrition',
                'urban planning and smart cities',
                'renewable energy and technology',
                'education in the digital age',
                'cultural diversity and globalization'
            ];

            const randomTopic = topics[Math.floor(Math.random() * topics.length)];

            const prompt = `
Act as a Cambridge B2 First exam creator. Generate a complete Reading & Use of English test about "${randomTopic}".

Create 3 parts:

PART 1 - Multiple Choice Cloze:
Create a text of 150-200 words about ${randomTopic} with 8 gaps. For each gap, provide 4 options (A, B, C, D) where only one is correct.

PART 2 - Open Cloze:
Create a text of 120-150 words about ${randomTopic} with 8 gaps that require single words (articles, prepositions, pronouns, etc.).

PART 3 - Word Formation:
Create a text of 100-120 words about ${randomTopic} with 8 gaps. Provide the base word in capitals that needs to be transformed.

OUTPUT FORMAT (JSON):
{
  "topic": "${randomTopic}",
  "parts": [
    {
      "partNumber": 1,
      "title": "Multiple Choice Cloze",
      "text": "Text with [GAP1], [GAP2], etc.",
      "questions": [
        {
          "gapNumber": 1,
          "options": ["option1", "option2", "option3", "option4"],
          "correctIndex": 0,
          "explanation": "Why this option is correct"
        }
      ]
    },
    {
      "partNumber": 2,
      "title": "Open Cloze",
      "text": "Text with [GAP1], [GAP2], etc.",
      "answers": ["word1", "word2", "word3", "word4", "word5", "word6", "word7", "word8"]
    },
    {
      "partNumber": 3,
      "title": "Word Formation",
      "text": "Text with [GAP1], [GAP2], etc.",
      "questions": [
        {
          "gapNumber": 1,
          "baseWord": "DEVELOP",
          "correctAnswer": "development",
          "explanation": "Noun form needed here"
        }
      ]
    }
  ]
}

Generate fresh, engaging content at B2 level now.
`;

            return await callGeminiAPI(prompt);
        }

        async function generateWritingContent() {
            const topics = [
                'technology and happiness',
                'social media and real relationships',
                'environmental responsibility',
                'work-life balance',
                'cultural exchange and travel',
                'education and future careers',
                'healthy lifestyle choices',
                'urban vs rural living',
                'artificial intelligence in daily life',
                'sustainable consumption'
            ];

            const randomTopic = topics[Math.floor(Math.random() * topics.length)];

            const prompt = `
Act as a Cambridge B2 First exam creator. Generate a Writing Part 1 essay task about "${randomTopic}".

Create an engaging essay question with:
1. A clear statement/question to discuss
2. Three bullet points to guide the essay (two specific + "your own idea")
3. Word count requirement: 140-190 words

OUTPUT FORMAT (JSON):
{
  "topic": "${randomTopic}",
  "essayPrompt": "Main essay question/statement",
  "bulletPoints": [
    "First specific point to discuss",
    "Second specific point to discuss",
    "........................... (your own idea)"
  ],
  "instructions": "Write an essay using all the notes and giving reasons for your point of view."
}

Make it thought-provoking and relevant to B2 students.
`;

            return await callGeminiAPI(prompt);
        }

        // Reading Test Data
        const readingTestData = {
            parts: [
                {
                    title: "Multiple Choice Cloze",
                    timeLimit: 10,
                    questions: [
                        {
                            text: "The internet has fundamentally changed how we communicate and access information. Social media platforms have <span class='gap'>1</span> people to connect with others around the world instantly. However, this technological advancement has also <span class='gap'>2</span> new challenges for society.",
                            options: [
                                ["enabled", "allowed", "permitted", "let"],
                                ["created", "made", "formed", "built"]
                            ],
                            correct: [0, 0]
                        },
                        {
                            text: "Climate change is one of the most pressing issues of our time. Scientists have been studying this phenomenon for decades, and their research <span class='gap'>3</span> that immediate action is needed. Many countries have <span class='gap'>4</span> to reduce their carbon emissions.",
                            options: [
                                ["shows", "demonstrates", "proves", "indicates"],
                                ["promised", "agreed", "committed", "decided"]
                            ],
                            correct: [0, 2]
                        }
                    ]
                },
                {
                    title: "Open Cloze",
                    timeLimit: 8,
                    questions: [
                        {
                            text: "Education is <span class='gap'>1</span> of the most important factors in personal development. It provides people <span class='gap'>2</span> the knowledge and skills they need to succeed in life.",
                            gaps: 2,
                            correct: ["one", "with"]
                        }
                    ]
                },
                {
                    title: "Word Formation",
                    timeLimit: 8,
                    questions: [
                        {
                            text: "The <span class='gap'>1</span> of renewable energy sources is crucial for our planet's future.",
                            keyword: "DEVELOP",
                            correct: "development"
                        }
                    ]
                }
            ]
        };

        async function generateAndInitializeReadingTest() {
            showLoadingScreen('Generating Reading & Use of English test...');

            try {
                const generatedText = await generateReadingContent();

                // Try to parse JSON response
                try {
                    generatedContent = JSON.parse(generatedText);
                    console.log('Generated content:', generatedContent);
                } catch (parseError) {
                    console.warn('Failed to parse Gemini response, using fallback');
                    generatedContent = createFallbackReadingContent();
                }

                initializeReadingTest();

            } catch (error) {
                console.error('Failed to generate content:', error);
                generatedContent = createFallbackReadingContent();
                initializeReadingTest();
            }
        }

        async function generateAndInitializeWritingTest() {
            showLoadingScreen('Generating Writing essay topic...');

            try {
                const generatedText = await generateWritingContent();

                try {
                    generatedContent = JSON.parse(generatedText);
                    console.log('Generated writing content:', generatedContent);
                } catch (parseError) {
                    console.warn('Failed to parse Gemini response, using fallback');
                    generatedContent = createFallbackWritingContent();
                }

                initializeWritingTest();

            } catch (error) {
                console.error('Failed to generate content:', error);
                generatedContent = createFallbackWritingContent();
                initializeWritingTest();
            }
        }

        function showLoadingScreen(message) {
            document.getElementById('modalTitle').textContent = 'AI Content Generation';
            document.getElementById('modalContent').innerHTML = `
                <div class="text-center py-12">
                    <div class="mb-6">
                        <div class="animate-spin rounded-full h-16 w-16 border-b-4 border-cambridge-blue mx-auto"></div>
                    </div>
                    <h3 class="text-xl font-semibold text-cambridge-gray-700 mb-4">🤖 ${message}</h3>
                    <p class="text-cambridge-gray-600 mb-6">
                        Google Gemini AI is creating personalized questions just for you...
                    </p>
                    <div class="max-w-md mx-auto">
                        <div class="bg-cambridge-gray-200 rounded-full h-2">
                            <div class="bg-cambridge-blue h-2 rounded-full animate-pulse" style="width: 60%"></div>
                        </div>
                        <p class="text-sm text-cambridge-gray-500 mt-2">This may take a few seconds</p>
                    </div>
                </div>
            `;
            document.getElementById('testModal').classList.remove('hidden');
        }

        function createFallbackReadingContent() {
            return {
                topic: "Digital Technology and Modern Life",
                parts: [
                    {
                        partNumber: 1,
                        title: "Multiple Choice Cloze",
                        text: "Digital technology has [GAP1] transformed the way we live and work. Social media platforms have [GAP2] people to connect instantly across the globe. However, this rapid technological advancement has also [GAP3] new challenges for society. Many experts [GAP4] that we need to find a better balance between our digital and offline lives.",
                        questions: [
                            {
                                gapNumber: 1,
                                options: ["completely", "hardly", "slowly", "barely"],
                                correctIndex: 0,
                                explanation: "'Completely' emphasizes the total transformation caused by digital technology."
                            },
                            {
                                gapNumber: 2,
                                options: ["enabled", "prevented", "discouraged", "forbidden"],
                                correctIndex: 0,
                                explanation: "'Enabled' means to make something possible, which fits the context of social media connecting people."
                            },
                            {
                                gapNumber: 3,
                                options: ["solved", "created", "avoided", "ignored"],
                                correctIndex: 1,
                                explanation: "'Created' indicates that technology has brought about new challenges."
                            },
                            {
                                gapNumber: 4,
                                options: ["deny", "argue", "refuse", "reject"],
                                correctIndex: 1,
                                explanation: "'Argue' means to present reasons for a particular viewpoint, which experts do."
                            }
                        ]
                    },
                    {
                        partNumber: 2,
                        title: "Open Cloze",
                        text: "The internet has become [GAP1] essential part of modern life. People [GAP2] all ages use it for communication, entertainment, and learning. [GAP3] the convenience it offers, many individuals spend [GAP4] much time online. This can lead [GAP5] problems such as social isolation and reduced physical activity.",
                        answers: ["an", "of", "Despite", "too", "to"]
                    },
                    {
                        partNumber: 3,
                        title: "Word Formation",
                        text: "The [GAP1] of artificial intelligence has raised important questions about the future of work. Many [GAP2] believe that AI will create new job opportunities while making others obsolete. The [GAP3] of this technology requires careful planning and consideration.",
                        questions: [
                            {
                                gapNumber: 1,
                                baseWord: "DEVELOP",
                                correctAnswer: "development",
                                explanation: "Noun form needed - 'the development of AI'"
                            },
                            {
                                gapNumber: 2,
                                baseWord: "ECONOMY",
                                correctAnswer: "economists",
                                explanation: "Plural noun needed - 'many economists believe'"
                            },
                            {
                                gapNumber: 3,
                                baseWord: "IMPLEMENT",
                                correctAnswer: "implementation",
                                explanation: "Noun form needed - 'the implementation of technology'"
                            }
                        ]
                    }
                ]
            };
        }

        function createFallbackWritingContent() {
            return {
                topic: "technology and happiness",
                essayPrompt: "Technology has made our lives easier, but has it made us happier? Discuss.",
                bulletPoints: [
                    "Communication and relationships",
                    "Work and productivity",
                    "........................... (your own idea)"
                ],
                instructions: "Write an essay using all the notes and giving reasons for your point of view."
            };
        }

        function initializeReadingTest() {
            currentTest = 'reading';
            currentPart = 0;
            currentQuestion = 0;
            userAnswers = {};

            if (!practiceMode) {
                timeRemaining = 75 * 60; // 75 minutes in seconds
                startTimer();
            }

            showReadingTest();
        }

        function showReadingTest() {
            // Use generated content if available, otherwise fallback to static data
            const testData = generatedContent || readingTestData;
            const part = testData.parts[currentPart];

            document.getElementById('modalTitle').textContent = `Reading & Use of English - Part ${currentPart + 1}`;
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <!-- Timer (if not practice mode) -->
                    ${!practiceMode ? `
                        <div class="flex justify-between items-center p-3 bg-cambridge-gray-100 rounded-lg">
                            <span class="text-sm font-medium">Time Remaining:</span>
                            <span class="text-lg font-bold text-cambridge-blue" id="timer">${formatTime(timeRemaining)}</span>
                        </div>
                    ` : ''}

                    <!-- Topic Display -->
                    ${testData.topic ? `
                        <div class="p-3 bg-cambridge-blue bg-opacity-10 rounded-lg">
                            <h4 class="text-sm font-medium text-cambridge-blue">📚 Topic: ${testData.topic}</h4>
                        </div>
                    ` : ''}

                    <!-- Part Navigation -->
                    <div class="flex space-x-2 overflow-x-auto pb-2">
                        ${testData.parts.map((p, i) => `
                            <button onclick="goToPart(${i})"
                                    class="flex-shrink-0 px-4 py-2 rounded-lg text-sm font-medium ${
                                        i === currentPart
                                            ? 'bg-cambridge-blue text-white'
                                            : 'bg-cambridge-gray-100 text-cambridge-gray-700 hover:bg-cambridge-gray-200'
                                    }">
                                Part ${i + 1}
                                <div class="text-xs opacity-75">${p.title}</div>
                            </button>
                        `).join('')}
                    </div>

                    <div class="p-4 bg-blue-50 border border-cambridge-blue rounded-lg">
                        <h4 class="text-sm font-medium text-cambridge-blue mb-2">Part ${currentPart + 1}: ${part.title}</h4>
                        <p class="text-sm text-cambridge-gray-700">${getInstructions(currentPart)}</p>
                    </div>

                    <!-- Question Content -->
                    <div class="prose max-w-none">
                        <div class="text-cambridge-gray-700 leading-relaxed">
                            ${renderQuestionContent(currentPart, currentQuestion)}
                        </div>
                    </div>

                    <!-- Navigation -->
                    <div class="flex justify-between items-center pt-4 border-t">
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-cambridge-gray-600">
                                Question ${currentQuestion + 1} of ${part.questions.length} • Part ${currentPart + 1} of ${readingTestData.parts.length}
                            </span>
                            ${practiceMode ? '<span class="text-xs bg-cambridge-green text-white px-2 py-1 rounded">Practice Mode</span>' : ''}
                        </div>
                        <div class="space-x-2">
                            <button onclick="previousQuestion()"
                                    ${currentPart === 0 && currentQuestion === 0 ? 'disabled' : ''}
                                    class="btn-secondary disabled:opacity-50">Previous</button>
                            <button onclick="flagQuestion()" class="btn-secondary">
                                ${isQuestionFlagged() ? '🚩 Unflag' : '🏳️ Flag'}
                            </button>
                            <button onclick="nextQuestion()" class="btn-primary">
                                ${isLastQuestion() ? 'Finish Test' : 'Next'}
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('testModal').classList.remove('hidden');
        }

        function renderQuestionContent(partIndex, questionIndex) {
            const testData = generatedContent || readingTestData;
            const part = testData.parts[partIndex];

            // Handle different content structures
            if (partIndex === 0) { // Multiple Choice Cloze
                return renderMultipleChoiceCloze(part, questionIndex);
            } else if (partIndex === 1) { // Open Cloze
                return renderOpenCloze(part, questionIndex);
            } else if (partIndex === 2) { // Word Formation
                return renderWordFormation(part, questionIndex);
            }
        }

        function renderMultipleChoiceCloze(part, questionIndex) {
            // For generated content, we show the full text with all gaps
            if (generatedContent) {
                return `
                    <div class="mb-6">
                        ${part.text.replace(/\[GAP(\d+)\]/g, (match, num) => {
                            return `<span class="inline-block bg-cambridge-blue text-white px-2 py-1 rounded text-sm font-medium mx-1">${num}</span>`;
                        })}
                    </div>

                    ${part.questions.map((q, gapIndex) => `
                        <div class="mb-6 p-4 border-2 border-cambridge-blue rounded-lg">
                            <h4 class="font-medium text-cambridge-gray-700 mb-3">Question ${q.gapNumber}:</h4>
                            <div class="space-y-2">
                                ${q.options.map((option, optionIndex) => `
                                    <label class="flex items-center space-x-3 p-2 hover:bg-blue-50 rounded cursor-pointer">
                                        <input type="radio"
                                               name="q${partIndex}_${questionIndex}_${gapIndex}"
                                               value="${optionIndex}"
                                               onchange="saveAnswer(${partIndex}, ${questionIndex}, ${gapIndex}, ${optionIndex})"
                                               ${getUserAnswer(partIndex, questionIndex, gapIndex) === optionIndex ? 'checked' : ''}
                                               class="text-cambridge-blue">
                                        <span><strong>${String.fromCharCode(65 + optionIndex)}</strong> ${option}</span>
                                    </label>
                                `).join('')}
                            </div>
                            ${getUserAnswer(partIndex, questionIndex, gapIndex) !== undefined && q.explanation ? `
                                <div class="mt-3 p-3 bg-green-50 border border-cambridge-green rounded">
                                    <p class="text-sm text-cambridge-green"><strong>Explanation:</strong> ${q.explanation}</p>
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                `;
            }

            // Fallback to original structure for static data
            return "Fallback content for multiple choice";
        }

        function renderOpenCloze(part, questionIndex) {
            if (generatedContent) {
                return `
                    <div class="mb-6">
                        ${part.text.replace(/\[GAP(\d+)\]/g, (match, num) => {
                            const gapIndex = parseInt(num) - 1;
                            return `<input type="text"
                                           class="inline-block w-24 px-2 py-1 text-center border-b-2 border-cambridge-blue bg-transparent focus:outline-none focus:border-cambridge-blue focus:bg-blue-50 transition-colors mx-1"
                                           placeholder="${num}"
                                           value="${getUserAnswer(1, questionIndex, gapIndex) || ''}"
                                           onchange="saveAnswer(1, ${questionIndex}, ${gapIndex}, this.value)"
                                           maxlength="20">`;
                        })}
                    </div>

                    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h5 class="text-sm font-medium text-yellow-800 mb-2">💡 Tips for Open Cloze:</h5>
                        <ul class="text-sm text-yellow-700 space-y-1">
                            <li>• Look for grammatical clues (articles, prepositions, pronouns)</li>
                            <li>• Consider the context and meaning of the whole sentence</li>
                            <li>• Common words include: the, a, an, to, of, in, on, at, for, with, by</li>
                        </ul>
                    </div>

                    ${part.answers ? `
                        <div class="mt-4 p-3 bg-green-50 border border-cambridge-green rounded">
                            <p class="text-sm text-cambridge-green"><strong>Answers:</strong> ${part.answers.join(', ')}</p>
                        </div>
                    ` : ''}
                `;
            }
            return "Fallback content for open cloze";
        }

        function renderWordFormation(part, questionIndex) {
            if (generatedContent && part.questions) {
                const currentQ = part.questions[questionIndex] || part.questions[0];

                return `
                    <div class="mb-6">
                        ${part.text.replace(/\[GAP(\d+)\]/g, (match, num) => {
                            return `<input type="text"
                                           class="inline-block w-32 px-2 py-1 text-center border-b-2 border-cambridge-purple bg-transparent focus:outline-none focus:border-cambridge-purple focus:bg-purple-50 transition-colors mx-1"
                                           placeholder="Enter word"
                                           value="${getUserAnswer(2, questionIndex, 0) || ''}"
                                           onchange="saveAnswer(2, ${questionIndex}, 0, this.value)"
                                           maxlength="30">`;
                        })}
                    </div>

                    <div class="bg-cambridge-gray-50 p-6 rounded-lg border border-cambridge-gray-200">
                        <div class="text-center">
                            <h4 class="text-sm font-medium text-cambridge-gray-700 mb-3">Word to transform:</h4>
                            <div class="bg-cambridge-purple text-white px-6 py-4 rounded-lg text-2xl font-bold inline-block">
                                ${currentQ.baseWord}
                            </div>
                        </div>
                    </div>

                    ${getUserAnswer(2, questionIndex, 0) && currentQ.explanation ? `
                        <div class="mt-4 p-3 bg-green-50 border border-cambridge-green rounded">
                            <p class="text-sm text-cambridge-green"><strong>Explanation:</strong> ${currentQ.explanation}</p>
                        </div>
                    ` : ''}
                `;
            }
            return "Fallback content for word formation";
        }
                        ${question.text.replace(/<span class='gap'>(\d+)<\/span>/g, (match, num) => {
                            const gapIndex = parseInt(num) - 1;
                            return `<span class="inline-block bg-cambridge-blue text-white px-2 py-1 rounded text-sm font-medium mx-1">${num}</span>`;
                        })}
                    </div>

                    ${question.options.map((optionSet, gapIndex) => `
                        <div class="mb-6 p-4 border-2 border-cambridge-blue rounded-lg">
                            <h4 class="font-medium text-cambridge-gray-700 mb-3">Question ${gapIndex + 1}:</h4>
                            <div class="space-y-2">
                                ${optionSet.map((option, optionIndex) => `
                                    <label class="flex items-center space-x-3 p-2 hover:bg-blue-50 rounded cursor-pointer">
                                        <input type="radio"
                                               name="q${partIndex}_${questionIndex}_${gapIndex}"
                                               value="${optionIndex}"
                                               onchange="saveAnswer(${partIndex}, ${questionIndex}, ${gapIndex}, ${optionIndex})"
                                               ${getUserAnswer(partIndex, questionIndex, gapIndex) === optionIndex ? 'checked' : ''}
                                               class="text-cambridge-blue">
                                        <span><strong>${String.fromCharCode(65 + optionIndex)}</strong> ${option}</span>
                                    </label>
                                `).join('')}
                            </div>
                        </div>
                    `).join('')}
                `;
            } else if (partIndex === 1) { // Open Cloze
                return `
                    <div class="mb-6">
                        ${question.text.replace(/<span class='gap'>(\d+)<\/span>/g, (match, num) => {
                            const gapIndex = parseInt(num) - 1;
                            return `<input type="text"
                                           class="inline-block w-24 px-2 py-1 text-center border-b-2 border-cambridge-blue bg-transparent focus:outline-none focus:border-cambridge-blue focus:bg-blue-50 transition-colors mx-1"
                                           placeholder="${num}"
                                           value="${getUserAnswer(partIndex, questionIndex, gapIndex) || ''}"
                                           onchange="saveAnswer(${partIndex}, ${questionIndex}, ${gapIndex}, this.value)"
                                           maxlength="20">`;
                        })}
                    </div>

                    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h5 class="text-sm font-medium text-yellow-800 mb-2">💡 Tips for Open Cloze:</h5>
                        <ul class="text-sm text-yellow-700 space-y-1">
                            <li>• Look for grammatical clues (articles, prepositions, pronouns)</li>
                            <li>• Consider the context and meaning of the whole sentence</li>
                            <li>• Common words include: the, a, an, to, of, in, on, at, for, with, by</li>
                        </ul>
                    </div>
                `;
            } else if (partIndex === 2) { // Word Formation
                return `
                    <div class="mb-6">
                        ${question.text.replace(/<span class='gap'>(\d+)<\/span>/g, (match, num) => {
                            return `<input type="text"
                                           class="inline-block w-32 px-2 py-1 text-center border-b-2 border-cambridge-purple bg-transparent focus:outline-none focus:border-cambridge-purple focus:bg-purple-50 transition-colors mx-1"
                                           placeholder="Enter word"
                                           value="${getUserAnswer(partIndex, questionIndex, 0) || ''}"
                                           onchange="saveAnswer(${partIndex}, ${questionIndex}, 0, this.value)"
                                           maxlength="30">`;
                        })}
                    </div>

                    <div class="bg-cambridge-gray-50 p-6 rounded-lg border border-cambridge-gray-200">
                        <div class="text-center">
                            <h4 class="text-sm font-medium text-cambridge-gray-700 mb-3">Word to transform:</h4>
                            <div class="bg-cambridge-purple text-white px-6 py-4 rounded-lg text-2xl font-bold inline-block">
                                ${question.keyword}
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // Helper functions
        function getInstructions(partIndex) {
            const instructions = [
                "For questions 1-8, read the text below and decide which answer (A, B, C or D) best fits each gap.",
                "For questions 9-16, read the text below and think of the word which best fits each gap. Use only one word in each gap.",
                "For questions 17-24, read the text below. Use the word given in capitals to form a word that fits in the gap."
            ];
            return instructions[partIndex] || "Complete the questions below.";
        }

        function saveAnswer(partIndex, questionIndex, gapIndex, answer) {
            const key = `${partIndex}_${questionIndex}_${gapIndex}`;
            userAnswers[key] = answer;
        }

        function getUserAnswer(partIndex, questionIndex, gapIndex) {
            const key = `${partIndex}_${questionIndex}_${gapIndex}`;
            return userAnswers[key];
        }

        function isQuestionFlagged() {
            const key = `flag_${currentPart}_${currentQuestion}`;
            return userAnswers[key] === true;
        }

        function flagQuestion() {
            const key = `flag_${currentPart}_${currentQuestion}`;
            userAnswers[key] = !userAnswers[key];
            showReadingTest(); // Refresh to update flag status
        }

        function isLastQuestion() {
            const testData = generatedContent || readingTestData;
            return currentPart === testData.parts.length - 1 &&
                   currentQuestion === (testData.parts[currentPart].questions ? testData.parts[currentPart].questions.length - 1 : 0);
        }

        function nextQuestion() {
            if (isLastQuestion()) {
                finishTest();
                return;
            }

            const testData = generatedContent || readingTestData;
            const currentPartData = testData.parts[currentPart];
            const questionsLength = currentPartData.questions ? currentPartData.questions.length : 1;

            if (currentQuestion < questionsLength - 1) {
                currentQuestion++;
            } else {
                currentPart++;
                currentQuestion = 0;
            }
            showReadingTest();
        }

        function previousQuestion() {
            if (currentPart === 0 && currentQuestion === 0) return;

            if (currentQuestion > 0) {
                currentQuestion--;
            } else {
                currentPart--;
                const testData = generatedContent || readingTestData;
                const prevPartData = testData.parts[currentPart];
                currentQuestion = (prevPartData.questions ? prevPartData.questions.length : 1) - 1;
            }
            showReadingTest();
        }

        function goToPart(partIndex) {
            currentPart = partIndex;
            currentQuestion = 0;
            showReadingTest();
        }

        function finishTest() {
            if (timerInterval) {
                clearInterval(timerInterval);
            }

            showTestResults();
        }

        function showTestResults() {
            document.getElementById('modalTitle').textContent = 'Test Results';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="text-center p-6 bg-cambridge-green bg-opacity-10 rounded-lg">
                        <h3 class="text-2xl font-bold text-cambridge-green mb-2">🎉 Test Completed!</h3>
                        <p class="text-cambridge-gray-600">You have successfully completed the Reading & Use of English test.</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center p-4 bg-cambridge-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-cambridge-blue">${Object.keys(userAnswers).filter(k => !k.startsWith('flag_')).length}</div>
                            <div class="text-sm text-cambridge-gray-600">Questions Attempted</div>
                        </div>
                        <div class="text-center p-4 bg-cambridge-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-cambridge-orange">${Object.keys(userAnswers).filter(k => k.startsWith('flag_')).length}</div>
                            <div class="text-sm text-cambridge-gray-600">Questions Flagged</div>
                        </div>
                        <div class="text-center p-4 bg-cambridge-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-cambridge-purple">${practiceMode ? 'Practice' : formatTime(timeRemaining)}</div>
                            <div class="text-sm text-cambridge-gray-600">${practiceMode ? 'Mode' : 'Time Left'}</div>
                        </div>
                    </div>

                    <div class="p-4 bg-blue-50 border border-cambridge-blue rounded-lg">
                        <h4 class="text-sm font-medium text-cambridge-blue mb-2">📊 Performance Summary:</h4>
                        <ul class="text-sm text-cambridge-gray-700 space-y-1">
                            <li>• Part 1 (Multiple Choice): ${getPartProgress(0)} questions attempted</li>
                            <li>• Part 2 (Open Cloze): ${getPartProgress(1)} questions attempted</li>
                            <li>• Part 3 (Word Formation): ${getPartProgress(2)} questions attempted</li>
                        </ul>
                    </div>

                    <div class="flex justify-center space-x-4">
                        <button onclick="reviewAnswers()" class="btn-secondary">Review Answers</button>
                        <button onclick="startNewTest()" class="btn-primary">Start New Test</button>
                        <button onclick="closeModal()" class="btn-secondary">Back to Dashboard</button>
                    </div>
                </div>
            `;
        }

        function getPartProgress(partIndex) {
            const part = readingTestData.parts[partIndex];
            let attempted = 0;

            for (let q = 0; q < part.questions.length; q++) {
                const question = part.questions[q];
                if (partIndex === 0) { // Multiple choice
                    for (let g = 0; g < question.options.length; g++) {
                        if (getUserAnswer(partIndex, q, g) !== undefined) attempted++;
                    }
                } else { // Other types
                    if (getUserAnswer(partIndex, q, 0) !== undefined) attempted++;
                }
            }

            return attempted;
        }

        // Timer functions
        function startTimer() {
            if (timerInterval) clearInterval(timerInterval);

            timerInterval = setInterval(() => {
                timeRemaining--;
                updateTimerDisplay();

                if (timeRemaining <= 0) {
                    clearInterval(timerInterval);
                    finishTest();
                }
            }, 1000);
        }

        function updateTimerDisplay() {
            const timerElement = document.getElementById('timer');
            if (timerElement) {
                timerElement.textContent = formatTime(timeRemaining);

                // Change color based on time remaining
                if (timeRemaining <= 300) { // 5 minutes
                    timerElement.className = 'text-lg font-bold text-cambridge-red';
                } else if (timeRemaining <= 900) { // 15 minutes
                    timerElement.className = 'text-lg font-bold text-cambridge-orange';
                } else {
                    timerElement.className = 'text-lg font-bold text-cambridge-blue';
                }
            }
        }

        function formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;

            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }

        // Writing Test
        function initializeWritingTest() {
            currentTest = 'writing';
            practiceMode = practiceMode || false;

            if (!practiceMode) {
                timeRemaining = 80 * 60; // 80 minutes
                startTimer();
            }

            showWritingTest();
        }

        function showWritingTest() {
            const writingData = generatedContent || createFallbackWritingContent();

            document.getElementById('modalTitle').textContent = 'Writing - Essay';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    ${!practiceMode ? `
                        <div class="flex justify-between items-center p-3 bg-cambridge-gray-100 rounded-lg">
                            <span class="text-sm font-medium">Time Remaining:</span>
                            <span class="text-lg font-bold text-cambridge-blue" id="timer">${formatTime(timeRemaining)}</span>
                        </div>
                    ` : ''}

                    <div class="p-4 bg-green-50 border border-cambridge-green rounded-lg">
                        <h4 class="text-sm font-medium text-cambridge-green mb-2">Instructions:</h4>
                        <p class="text-sm text-cambridge-gray-700">Write an essay of <strong>140-190 words</strong> using all the notes provided. Give reasons for your point of view.</p>
                    </div>

                    <!-- Topic Display -->
                    ${writingData.topic ? `
                        <div class="p-3 bg-cambridge-green bg-opacity-10 rounded-lg">
                            <h4 class="text-sm font-medium text-cambridge-green">✍️ Topic: ${writingData.topic}</h4>
                        </div>
                    ` : ''}

                    <div class="p-6 bg-white border-2 border-cambridge-gray-200 rounded-lg">
                        <h3 class="text-lg font-semibold text-cambridge-gray-700 mb-4">Essay Topic:</h3>
                        <p class="text-cambridge-gray-700 leading-relaxed mb-4">
                            <strong>${writingData.essayPrompt}</strong>
                        </p>
                        <div class="bg-cambridge-gray-50 p-4 rounded">
                            <h4 class="font-medium mb-2">Write about:</h4>
                            <ul class="space-y-1 text-sm">
                                ${writingData.bulletPoints.map((point, index) => `
                                    <li>${index + 1}. ${point}</li>
                                `).join('')}
                            </ul>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-cambridge-gray-700">Your Essay:</h4>
                            <div class="text-sm font-medium" id="wordCountDisplay">0 / 140-190 words</div>
                        </div>

                        <textarea
                            id="essayText"
                            class="w-full h-64 p-4 border-2 border-cambridge-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-cambridge-green focus:border-transparent resize-none"
                            placeholder="Start writing your essay here...

Example structure:
- Introduction: State your opinion
- Body paragraph 1: Communication and relationships
- Body paragraph 2: Work and productivity
- Body paragraph 3: Your own idea
- Conclusion: Summarize your view

Remember to use linking words like 'However', 'Furthermore', 'In conclusion'..."
                            oninput="updateWritingStats()"
                        >${userAnswers.essay || ''}</textarea>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-center">
                            <div class="p-3 bg-cambridge-gray-50 rounded">
                                <div class="text-2xl font-bold text-cambridge-blue" id="wordCount">0</div>
                                <div class="text-cambridge-gray-600">Words</div>
                            </div>
                            <div class="p-3 bg-cambridge-gray-50 rounded">
                                <div class="text-2xl font-bold text-cambridge-green" id="paragraphCount">0</div>
                                <div class="text-cambridge-gray-600">Paragraphs</div>
                            </div>
                            <div class="p-3 bg-cambridge-gray-50 rounded">
                                <div class="text-2xl font-bold text-cambridge-purple" id="sentenceCount">0</div>
                                <div class="text-cambridge-gray-600">Sentences</div>
                            </div>
                            <div class="p-3 bg-cambridge-gray-50 rounded">
                                <div class="text-2xl font-bold text-cambridge-orange" id="progressPercent">0%</div>
                                <div class="text-cambridge-gray-600">Complete</div>
                            </div>
                        </div>

                        <div class="w-full bg-cambridge-gray-200 rounded-full h-2">
                            <div id="progressBar" class="bg-cambridge-green h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center pt-4 border-t">
                        <span class="text-sm text-cambridge-gray-600">Part 1 • Essay (Compulsory)</span>
                        <div class="space-x-2">
                            <button onclick="saveDraft()" class="btn-secondary">Save Draft</button>
                            <button onclick="submitEssay()" class="btn-primary">Submit Essay</button>
                            <button onclick="closeModal()" class="btn-secondary">Back</button>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('testModal').classList.remove('hidden');
            updateWritingStats();
        }

        function updateWritingStats() {
            const textarea = document.getElementById('essayText');
            if (!textarea) return;

            const text = textarea.value;
            userAnswers.essay = text;

            const words = text.trim().split(/\s+/).filter(word => word.length > 0);
            const paragraphs = text.split('\n\n').filter(p => p.trim()).length;
            const sentences = text.split(/[.!?]+/).filter(s => s.trim()).length;

            const wordCount = words.length;
            const progress = Math.round((wordCount / 190) * 100);

            document.getElementById('wordCount').textContent = wordCount;
            document.getElementById('paragraphCount').textContent = paragraphs;
            document.getElementById('sentenceCount').textContent = sentences;
            document.getElementById('progressPercent').textContent = progress + '%';

            // Update word count display with color coding
            const wordCountDisplay = document.getElementById('wordCountDisplay');
            if (wordCount < 140) {
                wordCountDisplay.textContent = `${wordCount} / 140-190 words`;
                wordCountDisplay.className = 'text-sm font-medium text-cambridge-orange';
            } else if (wordCount > 190) {
                wordCountDisplay.textContent = `${wordCount} / 140-190 words`;
                wordCountDisplay.className = 'text-sm font-medium text-cambridge-red';
            } else {
                wordCountDisplay.textContent = `${wordCount} / 140-190 words`;
                wordCountDisplay.className = 'text-sm font-medium text-cambridge-green';
            }

            // Update progress bar
            const progressBar = document.getElementById('progressBar');
            if (progressBar) {
                progressBar.style.width = Math.min(100, progress) + '%';

                if (wordCount < 140) {
                    progressBar.className = 'bg-cambridge-orange h-2 rounded-full transition-all duration-300';
                } else if (wordCount > 190) {
                    progressBar.className = 'bg-cambridge-red h-2 rounded-full transition-all duration-300';
                } else {
                    progressBar.className = 'bg-cambridge-green h-2 rounded-full transition-all duration-300';
                }
            }
        }

        // Additional functions
        function saveDraft() {
            alert('✅ Draft saved successfully!\n\nYour essay has been saved and you can continue working on it later.');
        }

        function submitEssay() {
            const wordCount = userAnswers.essay ? userAnswers.essay.trim().split(/\s+/).filter(word => word.length > 0).length : 0;

            if (wordCount < 140) {
                if (!confirm(`⚠️ Your essay has only ${wordCount} words.\n\nThe minimum requirement is 140 words. Do you want to submit anyway?`)) {
                    return;
                }
            }

            if (timerInterval) {
                clearInterval(timerInterval);
            }

            showWritingResults();
        }

        function showWritingResults() {
            const wordCount = userAnswers.essay ? userAnswers.essay.trim().split(/\s+/).filter(word => word.length > 0).length : 0;

            document.getElementById('modalTitle').textContent = 'Writing Assessment';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="text-center p-6 bg-cambridge-green bg-opacity-10 rounded-lg">
                        <h3 class="text-2xl font-bold text-cambridge-green mb-2">📝 Essay Submitted!</h3>
                        <p class="text-cambridge-gray-600">Your essay has been submitted for assessment.</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center p-4 bg-cambridge-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-cambridge-blue">${wordCount}</div>
                            <div class="text-sm text-cambridge-gray-600">Words Written</div>
                        </div>
                        <div class="text-center p-4 bg-cambridge-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-cambridge-green">${wordCount >= 140 && wordCount <= 190 ? 'Good' : 'Review'}</div>
                            <div class="text-sm text-cambridge-gray-600">Word Count</div>
                        </div>
                        <div class="text-center p-4 bg-cambridge-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-cambridge-purple">${practiceMode ? 'Practice' : formatTime(timeRemaining)}</div>
                            <div class="text-sm text-cambridge-gray-600">${practiceMode ? 'Mode' : 'Time Left'}</div>
                        </div>
                    </div>

                    <div class="p-4 bg-blue-50 border border-cambridge-blue rounded-lg">
                        <h4 class="text-sm font-medium text-cambridge-blue mb-2">🤖 AI Assessment Preview:</h4>
                        <div class="space-y-2 text-sm text-cambridge-gray-700">
                            <div class="flex justify-between">
                                <span>Content:</span>
                                <span class="font-medium text-cambridge-green">4/5</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Organization:</span>
                                <span class="font-medium text-cambridge-green">4/5</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Language:</span>
                                <span class="font-medium text-cambridge-blue">3/5</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Communicative Achievement:</span>
                                <span class="font-medium text-cambridge-green">4/5</span>
                            </div>
                        </div>
                        <p class="text-xs text-cambridge-gray-600 mt-2">
                            * In the full version, detailed AI feedback would be provided here
                        </p>
                    </div>

                    <div class="flex justify-center space-x-4">
                        <button onclick="startNewTest()" class="btn-primary">Start New Test</button>
                        <button onclick="closeModal()" class="btn-secondary">Back to Dashboard</button>
                    </div>
                </div>
            `;
        }

        function reviewAnswers() {
            document.getElementById('modalTitle').textContent = 'Review Your Answers';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-cambridge-blue bg-opacity-10 rounded-lg">
                        <h3 class="text-lg font-bold text-cambridge-blue mb-2">📋 Answer Review</h3>
                        <p class="text-cambridge-gray-600">Review all your answers with detailed explanations and feedback.</p>
                    </div>

                    ${generateReviewContent()}

                    <div class="flex justify-center space-x-4">
                        <button onclick="startNewTest()" class="btn-primary">Start New Test</button>
                        <button onclick="closeModal()" class="btn-secondary">Back to Dashboard</button>
                    </div>
                </div>
            `;
        }

        function generateReviewContent() {
            const testData = generatedContent || readingTestData;
            let reviewHTML = '';

            testData.parts.forEach((part, partIndex) => {
                reviewHTML += `
                    <div class="card">
                        <h4 class="text-lg font-semibold text-cambridge-gray-700 mb-4">
                            Part ${part.partNumber || partIndex + 1}: ${part.title}
                        </h4>

                        ${generatePartReview(part, partIndex)}
                    </div>
                `;
            });

            return reviewHTML;
        }

        function generatePartReview(part, partIndex) {
            if (partIndex === 0 && generatedContent) { // Multiple Choice Cloze
                return part.questions.map((q, qIndex) => {
                    const userAnswer = getUserAnswer(partIndex, 0, qIndex);
                    const isCorrect = userAnswer === q.correctIndex;

                    return `
                        <div class="mb-4 p-4 border rounded-lg ${isCorrect ? 'border-cambridge-green bg-green-50' : userAnswer !== undefined ? 'border-cambridge-red bg-red-50' : 'border-cambridge-gray-300'}">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="font-medium">Question ${q.gapNumber}</h5>
                                <span class="text-sm px-2 py-1 rounded ${isCorrect ? 'bg-cambridge-green text-white' : userAnswer !== undefined ? 'bg-cambridge-red text-white' : 'bg-cambridge-gray-300 text-cambridge-gray-700'}">
                                    ${userAnswer !== undefined ? (isCorrect ? '✓ Correct' : '✗ Incorrect') : 'Not Answered'}
                                </span>
                            </div>

                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <strong>Your Answer:</strong>
                                    ${userAnswer !== undefined ? String.fromCharCode(65 + userAnswer) + ') ' + q.options[userAnswer] : 'Not answered'}
                                </div>
                                <div>
                                    <strong>Correct Answer:</strong>
                                    ${String.fromCharCode(65 + q.correctIndex)}) ${q.options[q.correctIndex]}
                                </div>
                            </div>

                            ${q.explanation ? `
                                <div class="mt-3 p-3 bg-cambridge-blue bg-opacity-10 rounded">
                                    <strong>Explanation:</strong> ${q.explanation}
                                </div>
                            ` : ''}
                        </div>
                    `;
                }).join('');
            } else if (partIndex === 1 && generatedContent) { // Open Cloze
                return `
                    <div class="mb-4 p-4 border border-cambridge-gray-300 rounded-lg">
                        <h5 class="font-medium mb-3">Your Answers:</h5>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            ${part.answers ? part.answers.map((correctAnswer, index) => {
                                const userAnswer = getUserAnswer(partIndex, 0, index);
                                const isCorrect = userAnswer && userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase();

                                return `
                                    <div class="flex items-center justify-between p-2 rounded ${isCorrect ? 'bg-green-50' : userAnswer ? 'bg-red-50' : 'bg-gray-50'}">
                                        <span>Gap ${index + 1}:</span>
                                        <div class="text-right">
                                            <div class="text-sm">
                                                <strong>You:</strong> ${userAnswer || 'Not answered'}
                                            </div>
                                            <div class="text-sm text-cambridge-green">
                                                <strong>Correct:</strong> ${correctAnswer}
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }).join('') : 'No answers available for review'}
                        </div>
                    </div>
                `;
            } else if (partIndex === 2 && generatedContent) { // Word Formation
                return part.questions.map((q, qIndex) => {
                    const userAnswer = getUserAnswer(partIndex, 0, qIndex);
                    const isCorrect = userAnswer && userAnswer.toLowerCase().trim() === q.correctAnswer.toLowerCase();

                    return `
                        <div class="mb-4 p-4 border rounded-lg ${isCorrect ? 'border-cambridge-green bg-green-50' : userAnswer ? 'border-cambridge-red bg-red-50' : 'border-cambridge-gray-300'}">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="font-medium">Gap ${q.gapNumber}</h5>
                                <span class="text-sm px-2 py-1 rounded ${isCorrect ? 'bg-cambridge-green text-white' : userAnswer ? 'bg-cambridge-red text-white' : 'bg-cambridge-gray-300 text-cambridge-gray-700'}">
                                    ${userAnswer ? (isCorrect ? '✓ Correct' : '✗ Incorrect') : 'Not Answered'}
                                </span>
                            </div>

                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div>
                                    <strong>Base Word:</strong> ${q.baseWord}
                                </div>
                                <div>
                                    <strong>Your Answer:</strong> ${userAnswer || 'Not answered'}
                                </div>
                                <div>
                                    <strong>Correct Answer:</strong> ${q.correctAnswer}
                                </div>
                            </div>

                            ${q.explanation ? `
                                <div class="mt-3 p-3 bg-cambridge-purple bg-opacity-10 rounded">
                                    <strong>Explanation:</strong> ${q.explanation}
                                </div>
                            ` : ''}
                        </div>
                    `;
                }).join('');
            }

            return '<p class="text-cambridge-gray-500">No review available for this part.</p>';
        }

        function startNewTest() {
            // Reset all state
            currentTest = null;
            currentPart = 0;
            currentQuestion = 0;
            userAnswers = {};
            practiceMode = false;
            timeRemaining = 0;

            if (timerInterval) {
                clearInterval(timerInterval);
                timerInterval = null;
            }

            closeModal();
            startFullTest();
        }

        function showComingSoon(testType) {
            document.getElementById('modalTitle').textContent = `${testType} Module`;
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6 text-center">
                    <div class="p-8">
                        <div class="text-6xl mb-4">🚧</div>
                        <h3 class="text-2xl font-bold text-cambridge-gray-700 mb-4">${testType} Module Coming Soon!</h3>
                        <p class="text-cambridge-gray-600 mb-6">
                            This module is currently in development and will include:
                        </p>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-2xl mx-auto">
                            ${testType === 'Listening' ? `
                                <div class="p-4 bg-cambridge-orange bg-opacity-10 rounded-lg">
                                    <h4 class="font-semibold text-cambridge-orange mb-2">🎧 Audio Features:</h4>
                                    <ul class="text-sm text-cambridge-gray-700 space-y-1">
                                        <li>• AI-generated audio content</li>
                                        <li>• Realistic exam conditions</li>
                                        <li>• Multiple accents and speakers</li>
                                        <li>• Automatic transcription</li>
                                    </ul>
                                </div>
                                <div class="p-4 bg-cambridge-blue bg-opacity-10 rounded-lg">
                                    <h4 class="font-semibold text-cambridge-blue mb-2">📝 Question Types:</h4>
                                    <ul class="text-sm text-cambridge-gray-700 space-y-1">
                                        <li>• Multiple choice questions</li>
                                        <li>• Sentence completion</li>
                                        <li>• Multiple matching</li>
                                        <li>• Note completion</li>
                                    </ul>
                                </div>
                            ` : `
                                <div class="p-4 bg-cambridge-purple bg-opacity-10 rounded-lg">
                                    <h4 class="font-semibold text-cambridge-purple mb-2">🎤 Speaking Features:</h4>
                                    <ul class="text-sm text-cambridge-gray-700 space-y-1">
                                        <li>• Voice recording capability</li>
                                        <li>• AI pronunciation analysis</li>
                                        <li>• Fluency assessment</li>
                                        <li>• Interactive conversations</li>
                                    </ul>
                                </div>
                                <div class="p-4 bg-cambridge-green bg-opacity-10 rounded-lg">
                                    <h4 class="font-semibold text-cambridge-green mb-2">🗣️ Test Parts:</h4>
                                    <ul class="text-sm text-cambridge-gray-700 space-y-1">
                                        <li>• Interview questions</li>
                                        <li>• Long turn presentations</li>
                                        <li>• Collaborative tasks</li>
                                        <li>• Discussion topics</li>
                                    </ul>
                                </div>
                            `}
                        </div>
                    </div>

                    <div class="flex justify-center space-x-4">
                        <button onclick="closeModal()" class="btn-primary">Back to Dashboard</button>
                        <button onclick="alert('📧 You will be notified when this module is ready!')" class="btn-secondary">Notify Me</button>
                    </div>
                </div>
            `;
            document.getElementById('testModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('testModal').classList.add('hidden');

            // Clean up timer if running
            if (timerInterval) {
                clearInterval(timerInterval);
                timerInterval = null;
            }
        }

        // Settings and other functions
        function showSettings() {
            alert('⚙️ Settings\n\nIn the full version, you could customize:\n\n• Practice vs Exam mode defaults\n• Timer preferences\n• Difficulty levels\n• Notification settings\n• Progress tracking\n• AI feedback detail level');
        }

        function showProgress() {
            alert('📊 Progress & Statistics\n\nIn the full version, you would see:\n\n• Detailed performance analytics\n• Progress over time\n• Strengths and weaknesses\n• Personalized study recommendations\n• Comparison with other students\n• Achievement badges');
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners to header buttons
            const settingsBtn = document.querySelector('button:contains("Settings")');
            const progressBtn = document.querySelector('button:contains("View Progress")');

            // Update button onclick handlers
            document.querySelectorAll('button').forEach(btn => {
                if (btn.textContent.includes('Settings')) {
                    btn.onclick = showSettings;
                } else if (btn.textContent.includes('View Progress') || btn.textContent.includes('View Statistics')) {
                    btn.onclick = showProgress;
                }
            });
        });
    </script>
</body>
</html>
