<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B2 First Trainer - Cambridge Exam Preparation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        cambridge: {
                            blue: '#0066CC',
                            green: '#00A651',
                            orange: '#FF6600',
                            purple: '#663399',
                            red: '#CC0000',
                            gray: {
                                50: '#F8F9FA',
                                100: '#E9ECEF',
                                200: '#DEE2E6',
                                300: '#CED4DA',
                                400: '#6C757D',
                                500: '#495057',
                                600: '#343A40',
                                700: '#212529'
                            }
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .btn-primary { @apply bg-cambridge-blue text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 font-medium; }
        .btn-secondary { @apply bg-cambridge-gray-100 text-cambridge-gray-700 px-4 py-2 rounded-md hover:bg-cambridge-gray-200 transition-colors duration-200 font-medium; }
        .card { @apply bg-white rounded-lg shadow-sm border border-cambridge-gray-200 p-6; }
    </style>
</head>
<body class="bg-cambridge-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-cambridge-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-cambridge-blue rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">🎯</span>
                        </div>
                        <h1 class="text-xl font-bold text-cambridge-gray-700">B2 First Trainer</h1>
                    </div>
                    <span class="text-sm text-cambridge-gray-500 bg-cambridge-gray-100 px-2 py-1 rounded">AI-Powered</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="btn-secondary">Settings</button>
                    <button class="btn-primary">View Progress</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Welcome Section -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-cambridge-gray-700 mb-2">Welcome to B2 First Trainer</h2>
            <p class="text-lg text-cambridge-gray-600 mb-6">
                Practice for your Cambridge B2 First exam with AI-generated exercises that adapt to your level.
                Choose a specific part to practice or take a full test.
            </p>
            
            <!-- Quick Actions -->
            <div class="flex flex-wrap gap-4 mb-8">
                <button onclick="startFullTest()" class="btn-primary flex items-center space-x-2">
                    <span>⏰</span>
                    <span>Take Full Test</span>
                </button>
                <button onclick="startPracticeMode()" class="btn-secondary flex items-center space-x-2">
                    <span>🎯</span>
                    <span>Practice Mode</span>
                </button>
                <button class="btn-secondary flex items-center space-x-2">
                    <span>📈</span>
                    <span>View Statistics</span>
                </button>
            </div>
        </div>

        <!-- Test Types Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" id="testTypesGrid">
            <!-- Reading & Use of English -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-cambridge-blue p-3 rounded-lg">
                        <span class="text-white text-xl">📚</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-cambridge-gray-700 mb-2">Reading & Use of English</h3>
                        <p class="text-cambridge-gray-600 mb-4">7 parts • 75 minutes • Grammar, vocabulary, and reading comprehension</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-cambridge-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-cambridge-gray-600 space-y-1">
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 1: Multiple Choice Cloze</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 2: Open Cloze</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 3: Word Formation</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 4: Key Word Transformation</span>
                                </li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="startTest('reading-use-of-english', 'full')" class="btn-primary text-sm">Full Test</button>
                            <button onclick="startTest('reading-use-of-english', 'parts')" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Writing -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-cambridge-green p-3 rounded-lg">
                        <span class="text-white text-xl">✍️</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-cambridge-gray-700 mb-2">Writing</h3>
                        <p class="text-cambridge-gray-600 mb-4">2 parts • 80 minutes • Essay and creative writing tasks</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-cambridge-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-cambridge-gray-600 space-y-1">
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 1: Essay (compulsory)</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 2: Article/Email/Letter/Report/Review</span>
                                </li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="startTest('writing', 'full')" class="btn-primary text-sm">Full Test</button>
                            <button onclick="startTest('writing', 'parts')" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Listening -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-cambridge-orange p-3 rounded-lg">
                        <span class="text-white text-xl">🎧</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-cambridge-gray-700 mb-2">Listening</h3>
                        <p class="text-cambridge-gray-600 mb-4">4 parts • 40 minutes • Audio comprehension and note-taking</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-cambridge-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-cambridge-gray-600 space-y-1">
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 1: Multiple Choice</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 2: Sentence Completion</span>
                                </li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="showComingSoon('Listening')" class="btn-primary text-sm">Full Test</button>
                            <button onclick="showComingSoon('Listening')" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Speaking -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-cambridge-purple p-3 rounded-lg">
                        <span class="text-white text-xl">🎤</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-cambridge-gray-700 mb-2">Speaking</h3>
                        <p class="text-cambridge-gray-600 mb-4">4 parts • 14 minutes • Interactive conversation and presentation</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-cambridge-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-cambridge-gray-600 space-y-1">
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 1: Interview</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-cambridge-gray-400 rounded-full"></span>
                                    <span>Part 2: Long Turn</span>
                                </li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="showComingSoon('Speaking')" class="btn-primary text-sm">Full Test</button>
                            <button onclick="showComingSoon('Speaking')" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center p-6">
                <div class="bg-cambridge-blue p-3 rounded-full w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                    <span class="text-white text-xl">🤖</span>
                </div>
                <h3 class="text-lg font-semibold text-cambridge-gray-700 mb-2">AI-Generated Content</h3>
                <p class="text-cambridge-gray-600">Dynamic exercises created by Google Gemini AI, ensuring fresh content every time you practice.</p>
            </div>
            
            <div class="text-center p-6">
                <div class="bg-cambridge-green p-3 rounded-full w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                    <span class="text-white text-xl">⏰</span>
                </div>
                <h3 class="text-lg font-semibold text-cambridge-gray-700 mb-2">Realistic Timing</h3>
                <p class="text-cambridge-gray-600">Practice with exact time limits from the real exam, or use practice mode without time pressure.</p>
            </div>
            
            <div class="text-center p-6">
                <div class="bg-cambridge-orange p-3 rounded-full w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                    <span class="text-white text-xl">📈</span>
                </div>
                <h3 class="text-lg font-semibold text-cambridge-gray-700 mb-2">Detailed Feedback</h3>
                <p class="text-cambridge-gray-600">Get instant AI-powered feedback with explanations and tips to improve your performance.</p>
            </div>
        </div>
    </main>

    <!-- Demo Test Modal -->
    <div id="testModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-cambridge-gray-700" id="modalTitle">Demo Test</h2>
                <button onclick="closeModal()" class="text-cambridge-gray-500 hover:text-cambridge-gray-700 text-2xl">&times;</button>
            </div>
            <div id="modalContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        function startFullTest() {
            alert('🚀 Full Test Mode\n\nThis would start a complete B2 First exam with all parts and realistic timing. In the full application, this includes:\n\n• Reading & Use of English (75 min)\n• Writing (80 min)\n• Listening (40 min)\n• Speaking (14 min)\n\nTotal: ~3.5 hours');
        }

        function startPracticeMode() {
            alert('🎯 Practice Mode\n\nThis would open the practice selection where you can:\n\n• Choose specific parts to practice\n• Work without time pressure\n• Get immediate feedback\n• Review explanations\n\nPerfect for focused learning!');
        }

        function startTest(testType, mode) {
            if (testType === 'reading-use-of-english') {
                showReadingDemo();
            } else if (testType === 'writing') {
                showWritingDemo();
            } else {
                showComingSoon(testType);
            }
        }

        function showReadingDemo() {
            document.getElementById('modalTitle').textContent = 'Reading & Use of English - Part 1 Demo';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-blue-50 border border-cambridge-blue rounded-lg">
                        <h4 class="text-sm font-medium text-cambridge-blue mb-2">Instructions:</h4>
                        <p class="text-sm text-cambridge-gray-700">For questions 1-8, read the text below and decide which answer (A, B, C or D) best fits each gap.</p>
                    </div>

                    <div class="prose max-w-none">
                        <h3 class="text-lg font-semibold mb-4">The Digital Revolution</h3>
                        <p class="text-cambridge-gray-700 leading-relaxed">
                            The internet has fundamentally changed how we communicate and access information. 
                            Social media platforms have <span class="bg-cambridge-blue text-white px-2 py-1 rounded font-medium">1</span> 
                            people to connect with others around the world instantly. However, this technological advancement 
                            has also <span class="bg-cambridge-blue text-white px-2 py-1 rounded font-medium">2</span> 
                            new challenges for society.
                        </p>
                    </div>

                    <div class="space-y-4">
                        <div class="border-2 border-cambridge-blue rounded-lg p-4 bg-blue-50">
                            <h4 class="font-medium text-cambridge-gray-700 mb-3">Question 1:</h4>
                            <div class="space-y-2">
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="A" class="text-cambridge-blue">
                                    <span><strong>A</strong> enabled</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="B" class="text-cambridge-blue">
                                    <span><strong>B</strong> allowed</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="C" class="text-cambridge-blue">
                                    <span><strong>C</strong> permitted</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="D" class="text-cambridge-blue">
                                    <span><strong>D</strong> let</span>
                                </label>
                            </div>
                        </div>

                        <div class="border-2 border-cambridge-purple rounded-lg p-4">
                            <h4 class="font-medium text-cambridge-gray-700 mb-3">Question 2:</h4>
                            <div class="space-y-2">
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="A" class="text-cambridge-purple">
                                    <span><strong>A</strong> created</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="B" class="text-cambridge-purple">
                                    <span><strong>B</strong> made</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="C" class="text-cambridge-purple">
                                    <span><strong>C</strong> formed</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="D" class="text-cambridge-purple">
                                    <span><strong>D</strong> built</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center pt-4 border-t">
                        <span class="text-sm text-cambridge-gray-600">Question 1 of 8 • Part 1 of 7</span>
                        <div class="space-x-2">
                            <button class="btn-secondary">Flag for Review</button>
                            <button class="btn-primary">Next Question</button>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('testModal').classList.remove('hidden');
        }

        function showWritingDemo() {
            document.getElementById('modalTitle').textContent = 'Writing - Essay Demo';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-green-50 border border-cambridge-green rounded-lg">
                        <h4 class="text-sm font-medium text-cambridge-green mb-2">Instructions:</h4>
                        <p class="text-sm text-cambridge-gray-700">Write an essay of <strong>140-190 words</strong> using all the notes provided. Give reasons for your point of view.</p>
                    </div>

                    <div class="p-6 bg-white border-2 border-cambridge-gray-200 rounded-lg">
                        <h3 class="text-lg font-semibold text-cambridge-gray-700 mb-4">Essay Topic:</h3>
                        <p class="text-cambridge-gray-700 leading-relaxed mb-4">
                            <strong>Technology has made our lives easier, but has it made us happier? Discuss.</strong>
                        </p>
                        <div class="bg-cambridge-gray-50 p-4 rounded">
                            <h4 class="font-medium mb-2">Write about:</h4>
                            <ul class="space-y-1 text-sm">
                                <li>1. Communication and relationships</li>
                                <li>2. Work and productivity</li>
                                <li>3. ........................... (your own idea)</li>
                            </ul>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-cambridge-gray-700">Your Essay:</h4>
                            <div class="text-sm font-medium text-cambridge-orange">0 / 140-190 words</div>
                        </div>

                        <textarea 
                            class="w-full h-64 p-4 border-2 border-cambridge-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-cambridge-green focus:border-transparent resize-none"
                            placeholder="Start writing your essay here...

Example structure:
- Introduction: State your opinion
- Body paragraph 1: Communication and relationships
- Body paragraph 2: Work and productivity  
- Body paragraph 3: Your own idea
- Conclusion: Summarize your view

Remember to use linking words like 'However', 'Furthermore', 'In conclusion'..."
                            oninput="updateWordCount(this)"
                        ></textarea>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-center">
                            <div class="p-3 bg-cambridge-gray-50 rounded">
                                <div class="text-2xl font-bold text-cambridge-blue" id="wordCount">0</div>
                                <div class="text-cambridge-gray-600">Words</div>
                            </div>
                            <div class="p-3 bg-cambridge-gray-50 rounded">
                                <div class="text-2xl font-bold text-cambridge-green" id="paragraphCount">0</div>
                                <div class="text-cambridge-gray-600">Paragraphs</div>
                            </div>
                            <div class="p-3 bg-cambridge-gray-50 rounded">
                                <div class="text-2xl font-bold text-cambridge-purple" id="sentenceCount">0</div>
                                <div class="text-cambridge-gray-600">Sentences</div>
                            </div>
                            <div class="p-3 bg-cambridge-gray-50 rounded">
                                <div class="text-2xl font-bold text-cambridge-orange" id="progressPercent">0%</div>
                                <div class="text-cambridge-gray-600">Complete</div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center pt-4 border-t">
                        <span class="text-sm text-cambridge-gray-600">Part 1 • Essay (Compulsory)</span>
                        <div class="space-x-2">
                            <button class="btn-secondary">Save Draft</button>
                            <button class="btn-primary">Submit Essay</button>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('testModal').classList.remove('hidden');
        }

        function updateWordCount(textarea) {
            const text = textarea.value;
            const words = text.trim().split(/\s+/).filter(word => word.length > 0);
            const paragraphs = text.split('\n\n').filter(p => p.trim()).length;
            const sentences = text.split(/[.!?]+/).filter(s => s.trim()).length;
            
            document.getElementById('wordCount').textContent = words.length;
            document.getElementById('paragraphCount').textContent = paragraphs;
            document.getElementById('sentenceCount').textContent = sentences;
            document.getElementById('progressPercent').textContent = Math.round((words.length / 190) * 100) + '%';
        }

        function showComingSoon(testType) {
            alert(`🚧 ${testType} Module Coming Soon!\n\nThis module is currently in development and will include:\n\n• Authentic ${testType.toLowerCase()} exercises\n• AI-generated content\n• Realistic exam conditions\n• Detailed feedback\n\nStay tuned for updates!`);
        }

        function closeModal() {
            document.getElementById('testModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('testModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
